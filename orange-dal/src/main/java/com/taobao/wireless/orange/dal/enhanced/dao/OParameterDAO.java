package com.taobao.wireless.orange.dal.enhanced.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface OParameterDAO extends IService<OParameterDO> {

    public boolean saveOrUpdateBatchByParameterId(List<OParameterDO> parameters);

    public List<OParameterDO> getParametersByParameterIds(List<String> parameterIds);

    public Map<String, OParameterDO> getParameterMapByParameterIds(List<String> parameterIds);
}
