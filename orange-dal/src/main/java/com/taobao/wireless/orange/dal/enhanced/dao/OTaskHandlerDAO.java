package com.taobao.wireless.orange.dal.enhanced.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskHandlerDO;

import java.util.Map;

/**
 * <p>
 * 任务处理人 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface OTaskHandlerDAO extends IService<OTaskHandlerDO> {

    Map<TaskHandlerStatus, Long> getCountGroupByStatus(String userId);

}
