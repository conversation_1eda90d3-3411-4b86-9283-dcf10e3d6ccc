package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.TaskService;
import com.taobao.wireless.orange.service.model.TaskDetailDTO;
import com.taobao.wireless.orange.service.model.TaskHandlerQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 任务管理接口
 */
@Api(tags = "任务管理接口")
@RestController
@RequestMapping("/api/tasks")
public class TaskController {

    @Autowired
    private TaskService taskService;

    @ApiOperation("查询我的任务列表")
    @GetMapping("/my")
    public PaginationResult<TaskDetailDTO> queryMyTasks(TaskHandlerQueryDTO query,
                                                        @RequestParam(defaultValue = "1") Integer page,
                                                        @RequestParam(defaultValue = "10") Integer size) {
        Pagination pagination = Pagination.builder()
                .pageNum(page)
                .pageSize(size)
                .build();
        return taskService.queryMyTasks(query, pagination);
    }

    @ApiOperation("忽略任务")
    @PutMapping("/{taskId}/ignore")
    public Result<Void> ignore(@PathVariable("taskId") String taskId) {
        taskService.ignore(taskId);
        return Result.success();
    }

    @ApiOperation("查询我的任务状态统计")
    @GetMapping("/my/count-by-status")
    public Result<Map<TaskHandlerStatus, Long>> countMyTaskByStatus() {
        return Result.success(taskService.countMyTaskByStatus());
    }

    @ApiOperation("转交任务")
    @PutMapping("/{taskId}/transfer")
    public Result<Void> transfer(@PathVariable("taskId") String taskId, @RequestParam String workNo) {
        taskService.transfer(taskId, workNo);
        return Result.success();
    }
}