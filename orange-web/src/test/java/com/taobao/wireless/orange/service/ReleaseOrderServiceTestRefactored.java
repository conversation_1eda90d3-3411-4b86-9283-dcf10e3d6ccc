package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.service.model.ReleaseOrderDTO;
import com.taobao.wireless.orange.service.model.ReleaseOrderOperationDTO;
import com.taobao.wireless.orange.service.model.ReleaseOrderQueryDTO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ReleaseOrderServiceTestRefactored extends BaseIntegrationTest {

    @Autowired
    private ReleaseOrderService releaseOrderService;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Test
    public void query() {
        ReleaseOrderQueryDTO releaseOrderQueryDTO = new ReleaseOrderQueryDTO();
        releaseOrderQueryDTO.setNamespaceId("cd3c20aa956e49bfb1909d0c80f3bda3");

        Pagination pagination = Pagination.builder()
                .pageNum(1)
                .pageSize(10)
                .build();
        PaginationResult<ReleaseOrderDTO> result = releaseOrderService.query(releaseOrderQueryDTO, pagination);
        assertSuccess(result, "查询发布单应该成功");
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 正常场景
     * 使用基类工具方法简化测试数据创建
     */
    @Test
    public void testGetOperations_WithExistingOperations_ShouldReturnOperationsList() {
        // 使用基类方法生成测试数据
        String testReleaseVersion = generateTestVersion("release");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();

        logTestInfo("测试获取操作记录 - 正常场景: " + testReleaseVersion);

        // 创建测试发布单
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 创建测试操作记录
        OReleaseOrderOperationDO operation1 = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                OperationType.CREATE, OperationStatus.SUCCESS);
        OReleaseOrderOperationDO operation2 = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                OperationType.VERIFY_REPLY, OperationStatus.INIT);
        releaseOrderOperationDAO.save(operation1);
        releaseOrderOperationDAO.save(operation2);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);

        // 使用基类断言方法
        assertSuccess(result, "获取操作记录应该成功");
        Assert.assertNotNull("操作列表不应为空", result.getData());
        Assert.assertEquals("应该返回2个操作记录", 2, result.getData().size());

        // 验证操作记录按ID降序排列
        List<ReleaseOrderOperationDTO> operations = result.getData();
        Assert.assertTrue("第一个操作的ID应该大于第二个", operations.get(0).getId() > operations.get(1).getId());

        // 验证操作记录内容
        ReleaseOrderOperationDTO firstOperation = operations.get(0);
        Assert.assertEquals("操作类型应该匹配", OperationType.VERIFY_REPLY, firstOperation.getType());
        Assert.assertEquals("操作状态应该匹配", OperationStatus.INIT, firstOperation.getStatus());

        logTestInfo("操作记录测试完成");
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 空结果场景
     */
    @Test
    public void testGetOperations_WithNoOperations_ShouldReturnEmptyList() {
        String testReleaseVersion = generateTestVersion("empty");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();

        logTestInfo("测试获取操作记录 - 空结果场景: " + testReleaseVersion);

        // 创建测试发布单但不创建操作记录
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);

        // 验证结果
        assertSuccess(result, "获取操作记录应该成功");
        Assert.assertNotNull("操作列表不应为null", result.getData());
        Assert.assertTrue("操作列表应该为空", result.getData().isEmpty());
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 不存在的发布版本
     */
    @Test
    public void testGetOperations_WithNonExistentReleaseVersion_ShouldReturnEmptyList() {
        // 使用基类方法生成不存在的发布版本
        String nonExistentReleaseVersion = generateTestVersion("nonexistent");

        logTestInfo("测试获取操作记录 - 不存在的版本: " + nonExistentReleaseVersion);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(nonExistentReleaseVersion, null);

        // 验证结果
        assertSuccess(result, "获取操作记录应该成功");
        Assert.assertNotNull("操作列表不应为null", result.getData());
        Assert.assertTrue("操作列表应该为空", result.getData().isEmpty());
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 多种操作类型和状态
     * 使用工作流验证方法
     */
    @Test
    public void testGetOperations_WithMultipleOperationTypesAndStatuses_ShouldReturnAllOperations() {
        String testReleaseVersion = generateTestVersion("multi");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();

        logTestInfo("测试获取操作记录 - 多种类型和状态: " + testReleaseVersion);

        // 使用工作流验证方法
        verifyWorkflow("多操作类型测试流程",
                () -> {
                    // 步骤1：创建测试发布单
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                    logTestInfo("创建测试发布单完成");
                },
                () -> {
                    // 步骤2：创建多种类型和状态的操作记录
                    OReleaseOrderOperationDO createOp = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                            OperationType.CREATE, OperationStatus.SUCCESS);
                    OReleaseOrderOperationDO verifyOp = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                            OperationType.VERIFY_REPLY, OperationStatus.SUCCESS);
                    OReleaseOrderOperationDO releaseOp = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                            OperationType.RELEASE, OperationStatus.INIT);
                    OReleaseOrderOperationDO failedOp = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                            OperationType.RATIO_GRAY, OperationStatus.FAILED);

                    releaseOrderOperationDAO.save(createOp);
                    releaseOrderOperationDAO.save(verifyOp);
                    releaseOrderOperationDAO.save(releaseOp);
                    releaseOrderOperationDAO.save(failedOp);
                    logTestInfo("创建多种操作记录完成");
                },
                () -> {
                    // 步骤3：验证结果
                    Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);
                    assertSuccess(result, "获取操作记录应该成功");
                    Assert.assertEquals("应该返回4个操作记录", 4, result.getData().size());

                    // 验证包含所有操作类型和状态
                    List<ReleaseOrderOperationDTO> operations = result.getData();
                    verifyReplyOperationTypes(operations);
                    verifyReplyOperationStatuses(operations);
                    logTestInfo("验证操作记录完成");
                }
        );
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 验证字段映射
     */
    @Test
    public void testGetOperations_FieldMapping_ShouldMapAllFieldsCorrectly() {
        String testReleaseVersion = generateTestVersion("mapping");
        String testAppKey = DEFAULT_APP_KEY;
        String testNamespaceId = generateTestId();
        String testParams = "{\"key\": \"value\"}";
        String testResult = "{\"status\": \"success\"}";

        logTestInfo("测试字段映射: " + testReleaseVersion);

        // 创建测试发布单
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testAppKey, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 创建包含所有字段的操作记录
        OReleaseOrderOperationDO operation = createTestOperation(testReleaseVersion, testAppKey, testNamespaceId,
                OperationType.CREATE, OperationStatus.SUCCESS);
        operation.setParams(testParams);
        operation.setResult(testResult);
        releaseOrderOperationDAO.save(operation);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);

        // 验证结果
        assertSuccess(result, "获取操作记录应该成功");
        Assert.assertEquals("应该返回1个操作记录", 1, result.getData().size());

        ReleaseOrderOperationDTO operationDTO = result.getData().get(0);
        verifyReplyOperationFieldMapping(operationDTO, testParams, testResult);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建测试发布单
     */
    private OReleaseOrderDO createTestReleaseOrder(String releaseVersion, String appKey, String namespaceId) {
        OReleaseOrderDO releaseOrder = new OReleaseOrderDO();
        releaseOrder.setReleaseVersion(releaseVersion);
        releaseOrder.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrder.setBizId(namespaceId);
        releaseOrder.setAppKey(appKey);
        releaseOrder.setNamespaceId(namespaceId);
        releaseOrder.setReleaseType(ReleaseType.PUBLISH);
        releaseOrder.setStatus(ReleaseOrderStatus.INIT);
        releaseOrder.setDescription("测试发布单 - " + releaseVersion);
        releaseOrder.setGmtCreate(getCurrentTime());
        releaseOrder.setGmtModified(getCurrentTime());
        return releaseOrder;
    }

    /**
     * 创建测试操作记录
     */
    private OReleaseOrderOperationDO createTestOperation(String releaseVersion, String appKey, String namespaceId,
                                                         OperationType type, OperationStatus status) {
        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(releaseVersion);
        operation.setAppKey(appKey);
        operation.setNamespaceId(namespaceId);
        operation.setType(type);
        operation.setStatus(status);
        operation.setGmtCreate(getCurrentTime());
        operation.setGmtModified(getCurrentTime());
        return operation;
    }

    /**
     * 验证操作类型
     */
    private void verifyReplyOperationTypes(List<ReleaseOrderOperationDTO> operations) {
        boolean hasCreate = operations.stream().anyMatch(op -> op.getType() == OperationType.CREATE);
        boolean hasVerify = operations.stream().anyMatch(op -> op.getType() == OperationType.VERIFY_REPLY);
        boolean hasRelease = operations.stream().anyMatch(op -> op.getType() == OperationType.RELEASE);
        boolean hasRatioGray = operations.stream().anyMatch(op -> op.getType() == OperationType.RATIO_GRAY);

        Assert.assertTrue("应该包含CREATE操作", hasCreate);
        Assert.assertTrue("应该包含VERIFY操作", hasVerify);
        Assert.assertTrue("应该包含RELEASE操作", hasRelease);
        Assert.assertTrue("应该包含RATIO_GRAY操作", hasRatioGray);
    }

    /**
     * 验证操作状态
     */
    private void verifyReplyOperationStatuses(List<ReleaseOrderOperationDTO> operations) {
        boolean hasSuccess = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.SUCCESS);
        boolean hasInit = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.INIT);
        boolean hasFailed = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.FAILED);

        Assert.assertTrue("应该包含SUCCESS状态", hasSuccess);
        Assert.assertTrue("应该包含INIT状态", hasInit);
        Assert.assertTrue("应该包含FAILED状态", hasFailed);
    }

    /**
     * 验证字段映射
     */
    private void verifyReplyOperationFieldMapping(ReleaseOrderOperationDTO operationDTO, String testParams, String testResult) {
        Assert.assertNotNull("ID不应为空", operationDTO.getId());
        Assert.assertEquals("操作类型应该匹配", OperationType.CREATE, operationDTO.getType());
        Assert.assertEquals("操作状态应该匹配", OperationStatus.SUCCESS, operationDTO.getStatus());
        Assert.assertEquals("参数应该匹配", testParams, operationDTO.getParams());
        Assert.assertEquals("结果应该匹配", testResult, operationDTO.getResult());
        Assert.assertNotNull("创建时间不应为空", operationDTO.getGmtCreate());
        Assert.assertNotNull("修改时间不应为空", operationDTO.getGmtModified());
    }
}
