package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskDO;
import lombok.Data;

import java.util.List;

/**
 * 任务业务对象
 */
@Data
public class TaskBO extends OTaskDO {
    /**
     * 任务状态列表（用于查询条件）
     */
    private List<TaskStatus> statuses;
    /**
     * 任务处理人列表
     */
    private List<TaskHandlerBO> handlers;

    /**
     * 搜索关键词（用于查询条件）
     */
    private String keyword;

    /**
     * 我的任务处理状态
     */
    private TaskHandlerStatus myHandlerStatus;
}