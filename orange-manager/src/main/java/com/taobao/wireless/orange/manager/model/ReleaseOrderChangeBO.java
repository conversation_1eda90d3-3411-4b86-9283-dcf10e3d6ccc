package com.taobao.wireless.orange.manager.model;

import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderChangeBO {
    private String namespaceId;
    /**
     * 上一个版本号
     */
    private String previousNamespaceVersion;
    /**
     * 上一个版本的参数
     */
    private List<ParameterBO> previousParameters;
    /**
     * 上一个版本的条件
     */
    private List<ConditionBO> previousConditions;
    /**
     * 参数变化
     */
    private List<ParameterVersionBO> parameterChanges;
    /**
     * 条件变化
     */
    private List<ConditionVersionBO> conditionChanges;
}
