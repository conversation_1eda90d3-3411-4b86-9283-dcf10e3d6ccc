package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 发起验证操作策略实现
 */
@Component
public class StartVerifyStrategy extends AbstractOperationTemplate {

    @Autowired
    private TaskManager taskManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.START_VERIFY;
    }

    @Override
    public void validateStatus(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (!ReleaseOrderStatus.IN_GRAY.equals(releaseOrder.getStatus()) && !ReleaseOrderStatus.IN_RATIO_GRAY.equals(releaseOrder.getStatus())) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public void executeOperation(OperationContext context) {
    }

    @Override
    public ReleaseOrderStatus getTargetStatus(OperationContext context) {
        return ReleaseOrderStatus.WAIT_VERIFY;
    }

    @Override
    @Async("taskExecutor")
    protected void handleTask(OperationContext context) {
        ONamespaceDO namespace = context.getNamespace();
        String releaseVersion = context.getReleaseVersion();
        taskManager.createVerifyTask(releaseVersion, namespace);
    }
}