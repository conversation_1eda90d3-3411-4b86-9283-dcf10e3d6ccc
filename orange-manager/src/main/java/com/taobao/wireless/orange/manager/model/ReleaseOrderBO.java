package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderBO extends OReleaseOrderDO {
    /**
     * 本次发布单涉及的参数变更
     */
    private List<ParameterVersionBO> parameterVersions;

    /**
     * 本次发布单涉及的条件变更
     */
    private List<ConditionVersionBO> conditionVersions;

    private List<ReleaseOrderStatus> statuses;
}
