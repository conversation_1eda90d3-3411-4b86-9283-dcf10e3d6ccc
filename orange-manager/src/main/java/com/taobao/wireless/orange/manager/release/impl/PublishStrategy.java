package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.ConditionManager;
import com.taobao.wireless.orange.manager.NamespaceVersionManager;
import com.taobao.wireless.orange.manager.config.generator.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.manager.config.model.Condition;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.manager.model.NamespaceVersionContentBO;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_RELEASE;

/**
 * 发布操作策略实现
 */
@Component
public class PublishStrategy extends AbstractOperationTemplate {

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private ConditionManager conditionManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.RELEASE;
    }

    @Override
    public void validateStatus(OperationContext context) {
        // 可以根据需要添加状态验证逻辑
        // if (!ReleaseOrderStatus.VERIFY_PASS.equals(context.getReleaseOrder().getStatus())) {
        //     throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_NOT_VERIFY_PASS);
        // }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public void executeOperation(OperationContext context) {
        String releaseVersion = context.getReleaseVersion();
        var releaseOrder = context.getReleaseOrder();

        // 上线参数版本
        onlineParameterVersion(releaseVersion);

        // 上线条件版本
        onlineConditionVersion(releaseVersion);

        // 上线参数条件版本
        onlineParameterConditionVersion(releaseVersion);

        // namespace 版本号递增
        var namespaceVersion = namespaceVersionManager.upgradeVersion(
                releaseOrder.getNamespaceId(), releaseVersion, FINISH_RELEASE);

        // 将正式版本内容保存（用于回滚）
        saveNamespaceVersionContent(releaseOrder, namespaceVersion);
    }

    @Override
    public ReleaseOrderStatus getTargetStatus(OperationContext context) {
        return ReleaseOrderStatus.RELEASED;
    }

    private void onlineParameterVersion(String releaseVersion) {
        List<OParameterVersionDO> parameters = parameterVersionDAO
                .lambdaQuery()
                .select(OParameterVersionDO::getParameterId, OParameterVersionDO::getChangeType)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameters)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        parameterVersionDAO.lambdaUpdate()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OParameterVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的参数对象状态置为删除
        List<String> deleteParameterIds = parameters.stream()
                .filter(p -> ChangeType.DELETE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, deleteParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.INVALID)
                    .update();
        }

        // 将新增的参数对象状态置从初始改为已发布
        List<String> newParameterIds = parameters.stream()
                .filter(p -> ChangeType.CREATE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, newParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.ONLINE)
                    .update();
        }
    }

    private void onlineConditionVersion(String releaseVersion) {
        List<OConditionVersionDO> conditions = conditionVersionDAO
                .lambdaQuery()
                .select(OConditionVersionDO::getConditionId, OConditionVersionDO::getChangeType)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> conditionIds = conditions.stream()
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        conditionVersionDAO.lambdaUpdate()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的条件对象状态置为删除
        List<String> deleteConditionIds = conditions.stream()
                .filter(c -> ChangeType.DELETE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, deleteConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.INVALID)
                    .update();
        }

        // 将新增的条件对象状态置为已发布
        List<String> newConditionIds = conditions.stream()
                .filter(c -> ChangeType.CREATE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, newConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.ONLINE)
                    .update();
        }
    }

    private void onlineParameterConditionVersion(String releaseVersion) {
        var parameterConditionVersions = parameterConditionVersionDAO.lambdaQuery()
                .select(OParameterConditionVersionDO::getParameterId, OParameterConditionVersionDO::getConditionId)
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }

        // 将历史发布版本标记为过期
        var updateChainWrapper = parameterConditionVersionDAO.lambdaUpdate();
        for (var parameterCondition : parameterConditionVersions) {
            updateChainWrapper.or(i -> i
                    .eq(OParameterConditionVersionDO::getParameterId, parameterCondition.getParameterId())
                    .eq(OParameterConditionVersionDO::getConditionId, parameterCondition.getConditionId())
                    .eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED));
        }
        updateChainWrapper.set(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED).update();

        // 将本次发布版本标记为发布
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();
    }

    /**
     * 将正式内容保存（用于回滚）
     *
     * @param releaseOrder     发布订单
     * @param namespaceVersion 命名空间版本
     */
    private void saveNamespaceVersionContent(OReleaseOrderDO releaseOrder, String namespaceVersion) {
        // 创建基础版本内容对象
        ONamespaceVersionContentDO versionContentDO = createBaseVersionContent(releaseOrder, namespaceVersion);

        // 生成并设置内容
        versionContentDO.setContent(generateContent(releaseOrder.getNamespaceId()));

        // 保存到数据库
        namespaceVersionContentDAO.save(versionContentDO);
    }

    /**
     * 创建基础版本内容对象
     */
    private ONamespaceVersionContentDO createBaseVersionContent(OReleaseOrderDO releaseOrder, String namespaceVersion) {
        ONamespaceVersionContentDO versionContentDO = new ONamespaceVersionContentDO();
        versionContentDO.setNamespaceId(releaseOrder.getNamespaceId());
        versionContentDO.setNamespaceVersion(namespaceVersion);
        versionContentDO.setAppKey(releaseOrder.getAppKey());
        return versionContentDO;
    }

    /**
     * 获取命名空间记录
     */
    private NamespaceIdNameRecord getNamespaceRecord(String namespaceId) {
        return Optional.ofNullable(namespaceDAO.getByNamespaceId(namespaceId))
                .map(n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName()))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }

    /**
     * 生成内容
     */
    private String generateContent(String namespaceId) {
        NamespaceIdNameRecord namespace = getNamespaceRecord(namespaceId);

        var releaseConfig = fullReleaseConfigGenerator.generate(namespace, "0");
        if (releaseConfig == null) {
            return null;
        }

        var namespaceVersionContent = new NamespaceVersionContentBO();

        // 设置参数信息
        namespaceVersionContent.setParameters(releaseConfig.getParameters());
        // 设置条件信息
        if (CollectionUtils.isNotEmpty(releaseConfig.getConditions())) {
            Pipe.of(releaseConfig.getConditions())
                    .map(this::buildConditionsList)
                    .apply(namespaceVersionContent::setConditions);
        }

        return JSON.toJSONString(namespaceVersionContent);
    }

    /**
     * 构建条件列表
     */
    private List<NamespaceVersionContentBO.Condition> buildConditionsList(List<Condition> conditions) {
        List<String> conditionIds = conditions.stream()
                .map(Condition::getId)
                .collect(Collectors.toList());

        // 批量获取条件版本信息和名称
        var conditionId2Version = conditionManager.getOnlineConditionVersionMap(conditionIds);
        var conditionId2Name = conditionDAO.getConditionId2NameMap(conditionIds);

        return conditions.stream()
                .map(c -> buildConditionContent(c, conditionId2Name, conditionId2Version))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个条件内容
     */
    private NamespaceVersionContentBO.Condition buildConditionContent(
            Condition condition,
            Map<String, String> conditionId2Name,
            Map<String, OConditionVersionDO> conditionId2Version) {

        var conditionContent = new NamespaceVersionContentBO.Condition();
        conditionContent.setConditionId(condition.getId());
        conditionContent.setName(conditionId2Name.get(condition.getId()));
        conditionContent.setExpression(condition.getExpression());

        var versionInfo = conditionId2Version.get(condition.getId());
        conditionContent.setReleaseVersion(versionInfo.getReleaseVersion());

        return conditionContent;
    }
}