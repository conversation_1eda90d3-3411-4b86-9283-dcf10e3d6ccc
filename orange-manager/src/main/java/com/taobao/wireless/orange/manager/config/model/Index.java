package com.taobao.wireless.orange.manager.config.model;


import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.common.model.proto.*;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Index implements ResourceSerializable {
    /**
     * 索引协议版本，用于指导如何解析和消费配置文件，方便后续协议扩展和升级
     * 是否可选：否
     * 示例值：1.0
     */
    private String schemaVersion;

    /**
     * CDN 域名，用于资源下载
     * 是否可选：否
     * 示例值：dorangesource.alicdn.com
     */
    private String cdn;

    /**
     * 应用标识，用于鉴权
     * 是否可选：否
     * 示例值：21380790
     */
    private String appKey;

    /**
     * 索引版本号，用户更新判断
     * 是否可选：否
     * 示例值：1620241009140003886
     */
    private long version;

    /**
     * 基础版本号，"0"代表全量索引，其他代表差量索引
     * 是否可选：否
     * 示例值：dsaf321
     */
    private long baseVersion;

    /**
     * 在一定阈值内需要下线的命名空间，仅适用于差量配置
     * 是否可选：是
     * 示例值：[]
     */
    private List<String> offlineNamespaces;

    /**
     * 配置发布策略，FULL/INCREMENTAL
     * 是否可选：否
     * 示例值：FULL
     */
    private ConfigStrategy strategy;

    /**
     * 命名空间数组，包含不同类型的配置数据
     * 是否可选：否
     * 类型：Array
     */
    private List<Namespace> namespaces;

    @Data
    @Builder
    public static class Namespace {
        /**
         * 命名空间名称
         * 是否可选：否
         * 示例值：network_harmony
         */
        private String name;

        /**
         * namespace 变更版本，如果版本大于本地版本则需要触发百分比计算
         * 是否可选：否
         * 示例值：1620241009140003886
         */
        private long changeVersion;

        /**
         * 正式发布配置，包括资源地址和 MD5 值
         * 是否可选：否
         */
        private Config release;

        /**
         * 灰度发布配置，包括资源地址和 MD5 值及发布单
         * 是否可选：是
         */
        private GrayConfig gray;

        /**
         * 实验配置，包括资源地址和 MD5 值
         * 是否可选：是
         */
        private Config experiment;
    }

    @Data
    @Builder
    public static class Config {
        /**
         * 资源地址
         * 是否可选：否
         * 示例值：nca82969ff08a84efeac17d6df181e26a0.json / rlt82969ff08a84sfeac17dwdf181e25a1.json
         */
        private String resourceId;

        /**
         * 资源内容的 MD5 值，用于内容变化判断
         * 是否可选：否
         * 示例值：a41cbbdedf8543de3d8a6bca31f59f54 / bcffd553235fa711b39f52a588e3224b
         */
        private String resourceMd5;
    }

    @Data
    @Builder
    public static class GrayConfig {
        /**
         * 发布单数组，用于灰度控制
         * 是否可选：否
         */
        private List<Order> orders;
        /**
         * 资源地址
         * 是否可选：否
         * 示例值：nca82969ff08a84efeac17d6df181e26a0.json / rlt82969ff08a84sfeac17dwdf181e25a1.json
         */
        private String resourceId;

        /**
         * 资源内容的 MD5 值，用于内容变化判断
         * 是否可选：否
         * 示例值：a41cbbdedf8543de3d8a6bca31f59f54 / bcffd553235fa711b39f52a588e3224b
         */
        private String resourceMd5;
    }

    @Data
    @Builder
    public static class Order {
        /**
         * 发布单版本，用于灰度打散设备
         * 是否可选：否
         * 示例值：433
         */
        private long version;

        /**
         * 灰度百分比(十万分之一为单位)，用于控制灰度覆盖
         * 是否可选：是
         * 示例值：100
         */
        private Integer percent;
    }

    @Override
    public byte[] serialize() {
        // fixme: 联调需要
        if ("json".equals(SwitchConfig.protocolType)) {
            return JSON.toJSONString(this).getBytes();
        }

        IndexProto.Builder builder = IndexProto.newBuilder()
                .setSchemaVersion(this.schemaVersion)
                .setCdn(this.cdn)
                .setAppKey(this.appKey)
                .setVersion(this.version)
                .setBaseVersion(this.baseVersion)
                .setStrategy(convertConfigStrategy(this.strategy));

        if (this.offlineNamespaces != null) {
            builder.addAllOfflineNamespaces(this.offlineNamespaces);
        }

        if (this.namespaces != null) {
            List<NamespaceProto> namespaceProtos = this.namespaces.stream()
                    .map(this::convertNamespace)
                    .collect(Collectors.toList());
            builder.addAllNamespaces(namespaceProtos);
        }

        return builder.build().toByteArray();
    }

    private ConfigStrategyProto convertConfigStrategy(ConfigStrategy configStrategy) {
        return ConfigStrategyProto.valueOf(configStrategy.getCode());
    }

    private NamespaceProto convertNamespace(Namespace namespace) {
        NamespaceProto.Builder builder = NamespaceProto.newBuilder()
                .setName(namespace.getName())
                .setChangeVersion(namespace.getChangeVersion());

        if (namespace.getRelease() != null) {
            builder.setRelease(convertConfig(namespace.getRelease()));
        }

        if (namespace.getGray() != null) {
            builder.setGray(convertGrayConfig(namespace.getGray()));
        }

        return builder.build();
    }

    private ConfigProto convertConfig(Config config) {
        return ConfigProto.newBuilder()
                .setResourceId(config.getResourceId())
                .setResourceMd5(config.getResourceMd5())
                .build();
    }

    private ConfigWithOrdersProto convertGrayConfig(GrayConfig grayConfig) {
        ConfigWithOrdersProto.Builder builder = ConfigWithOrdersProto.newBuilder()
                .setResourceId(grayConfig.getResourceId())
                .setResourceMd5(grayConfig.getResourceMd5());
        if (grayConfig.getOrders() != null) {
            List<OrderProto> orderProtos = grayConfig.getOrders().stream()
                    .map(this::convertOrder)
                    .collect(Collectors.toList());
            builder.addAllOrders(orderProtos);
        }
        return builder.build();
    }

    private OrderProto convertOrder(Order order) {
        return OrderProto.newBuilder()
                .setVersion(order.getVersion())
                .setPercent(order.getPercent())
                .build();
    }
}
