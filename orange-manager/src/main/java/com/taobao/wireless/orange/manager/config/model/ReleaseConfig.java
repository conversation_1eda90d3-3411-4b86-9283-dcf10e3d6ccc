package com.taobao.wireless.orange.manager.config.model;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import com.taobao.wireless.orange.common.model.proto.*;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Builder
@Data
public class ReleaseConfig implements ResourceSerializable {
    /**
     * 索引协议版本，用于指导解析配置文件
     * 必填字段
     * 示例值: "1.0"
     */
    private String schemaVersion;

    /**
     * 命名空间名称，代表配置所属的域
     * 必填字段
     * 示例值: "network_harmony"
     */
    private String namespace;

    /**
     * 正式配置存在差量和增量两种情况FULL/INCREMENTAL
     */
    private ConfigStrategy strategy;

    /**
     * 配置类型，指示配置的应用场景
     * 必填字段
     * 示例值: "release"
     */
    private ConfigType type;

    /**
     * 删除的参数，列出需要下线的参数键
     * 可选字段
     * 示例值: ["bootnamespace"]
     */
    private List<String> offlineParameters;

    /**
     * 条件数组，在特定条件下应用配置参数
     * 必填字段
     */
    private List<Condition> conditions;

    /**
     * 参数数组，定义具体的配置项和其条件值
     * 必填字段
     */
    private List<Parameter> parameters;

    @Override
    public byte[] serialize() {
        // fixme: 联调需要
        if ("json".equals(SwitchConfig.protocolType)) {
            return JSON.toJSONString(this).getBytes();
        }

        ReleaseConfigProto.Builder builder = ReleaseConfigProto.newBuilder()
                .setSchemaVersion(this.schemaVersion)
                .setNamespace(this.namespace)
                .setStrategy(convertConfigStrategy(this.strategy))
                .setType(convertConfigType(this.type));

        if (this.offlineParameters != null) {
            builder.addAllOfflineParameters(this.offlineParameters);
        }

        if (this.conditions != null) {
            List<ConditionProto> conditionProtos = this.conditions.stream()
                    .map(this::convertCondition)
                    .collect(Collectors.toList());
            builder.addAllConditions(conditionProtos);
        }

        if (this.parameters != null) {
            List<ParameterProto> parameterProtos = this.parameters.stream()
                    .map(this::convertParameter)
                    .collect(Collectors.toList());
            builder.addAllParameters(parameterProtos);
        }

        return builder.build().toByteArray();
    }

    /**
     * Convert ConfigStrategy to ConfigStrategyProto
     */
    private ConfigStrategyProto convertConfigStrategy(ConfigStrategy strategy) {
        if (strategy == null) {
            return ConfigStrategyProto.FULL; // Default value
        }
        return ConfigStrategyProto.valueOf(strategy.getCode());
    }

    /**
     * Convert ConfigType to ConfigTypeProto
     */
    private ConfigTypeProto convertConfigType(ConfigType type) {
        if (type == null) {
            return ConfigTypeProto.RELEASE; // Default value
        }
        switch (type) {
            case RELEASE:
                return ConfigTypeProto.RELEASE;
            case GRAY:
                return ConfigTypeProto.GRAY;
            case EXPERIMENT:
                return ConfigTypeProto.EXPERIMENT;
            default:
                throw new IllegalArgumentException("Unknown ConfigType: " + type);
        }
    }

    /**
     * Convert Condition to ConditionProto
     */
    private ConditionProto convertCondition(Condition condition) {
        ConditionProto.Builder builder = ConditionProto.newBuilder()
                .setId(condition.getId());

        if (condition.getExpression() != null) {
            builder.setExpression(convertExpression(condition.getExpression()));
        }

        return builder.build();
    }

    /**
     * Convert Expression to ExpressionProto
     */
    private ExpressionProto convertExpression(Expression expression) {
        ExpressionProto.Builder builder = ExpressionProto.newBuilder();

        if (expression.getOperator() != null) {
            builder.setOperator(expression.getOperator());
        }

        if (expression.getKey() != null) {
            builder.setKey(expression.getKey());
        }

        if (expression.getValue() != null) {
            builder.setValue(expression.getValue());
        }

        if (expression.getChildren() != null) {
            for (Expression child : expression.getChildren()) {
                builder.addChildren(convertExpression(child));
            }
        }

        return builder.build();
    }

    /**
     * Convert Parameter to ParameterProto
     */
    private ParameterProto convertParameter(Parameter parameter) {
        ParameterProto.Builder builder = ParameterProto.newBuilder()
                .setKey(parameter.getKey())
                .setVersion(parameter.getVersion());

        if (parameter.getValueType() != null) {
            // Convert string value type to enum
            switch (parameter.getValueType().toUpperCase()) {
                case "JSON":
                    builder.setValueType(ParameterValueTypeProto.JSON);
                    break;
                case "STRING":
                    builder.setValueType(ParameterValueTypeProto.STRING);
                    break;
                case "BOOLEAN":
                    builder.setValueType(ParameterValueTypeProto.BOOLEAN);
                    break;
                default:
                    // Default to STRING if unknown
                    builder.setValueType(ParameterValueTypeProto.STRING);
            }
        }

        if (parameter.getDefaultValue() != null) {
            // Handle different types of defaultValue
            if (parameter.getDefaultValue() instanceof Boolean) {
                builder.setDefaultBoolValue((Boolean) parameter.getDefaultValue());
            } else if (parameter.getDefaultValue() instanceof String) {
                builder.setDefaultStringValue((String) parameter.getDefaultValue());
            }
        }

        if (parameter.getConditionalValues() != null) {
            List<ConditionalValueProto> conditionalValueProtos = parameter.getConditionalValues().stream()
                    .map(this::convertConditionalValue)
                    .collect(Collectors.toList());
            builder.addAllConditionalValues(conditionalValueProtos);
        }

        return builder.build();
    }

    /**
     * Convert ConditionalValue to ConditionalValueProto
     */
    private ConditionalValueProto convertConditionalValue(ConditionalValue conditionalValue) {
        ConditionalValueProto.Builder builder = ConditionalValueProto.newBuilder()
                .setConditionId(conditionalValue.getConditionId());

        if (conditionalValue.getValue() != null) {
            // Handle different types of value
            if (conditionalValue.getValue() instanceof Boolean) {
                builder.setBoolValue((Boolean) conditionalValue.getValue());
            } else if (conditionalValue.getValue() instanceof String) {
                builder.setStringValue((String) conditionalValue.getValue());
            }
        }

        return builder.build();
    }
}
