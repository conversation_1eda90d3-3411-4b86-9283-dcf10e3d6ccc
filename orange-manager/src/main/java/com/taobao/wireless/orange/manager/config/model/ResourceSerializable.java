package com.taobao.wireless.orange.manager.config.model;

/**
 * Interface for objects that can be serialized to and deserialized from byte arrays.
 * Implementing classes should provide both serialization and deserialization methods.
 */
public interface ResourceSerializable {
    /**
     * Serialize this object to a byte array
     *
     * @return The serialized data
     */
    byte[] serialize();
}
