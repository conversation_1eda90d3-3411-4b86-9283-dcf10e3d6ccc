package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.manager.NamespaceVersionManager;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 灰度操作策略实现
 */
@Component
public class RatioGrayStrategy extends AbstractOperationTemplate {

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.RATIO_GRAY;
    }

    @Override
    public void validateStatus(OperationContext context) {
    }

    @Override
    public void validateParameters(OperationContext context) {
        // 灰度操作需要校验灰度百分比参数
        Object additionalData = context.getAdditionalData();
        int percent = (int) additionalData;
        if (percent < 0 || percent >= 100000) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度百分比必须在0-100000之间");
        }

        var releaseOrder = context.getReleaseOrder();
        if (releaseOrder.getPercent() != null && percent <= releaseOrder.getPercent()) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度百分比必须大于当前值");
        }
    }

    @Override
    public void executeOperation(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        namespaceVersionManager.upgradeChangeVersion(
            releaseOrder.getNamespaceId(),
            context.getReleaseVersion(),
            NamespaceVersionChangeType.RATIO_GRAY
        );
    }

    @Override
    public void updateReleaseOrderStatus(OperationContext context) {
        Integer percent = (Integer) context.getAdditionalData();

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .set(OReleaseOrderDO::getPercent, percent)
                .set(OReleaseOrderDO::getStatus, getTargetStatus(context))
                .update();
    }

    @Override
    public ReleaseOrderStatus getTargetStatus(OperationContext context) {
        return ReleaseOrderStatus.IN_RATIO_GRAY;
    }
}