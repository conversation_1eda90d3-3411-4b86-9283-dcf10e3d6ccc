package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import lombok.Data;

import java.util.List;

@Data
public class ParameterVersionBO extends OParameterVersionDO {
    private List<String> conditionNamesOrder;

    /**
     * 关联的参数
     */
    private ParameterBO parameterBO;

    /**
     * 涉及的参数条件变更
     */
    private List<ParameterConditionVersionBO> parameterConditionVersions;
}
