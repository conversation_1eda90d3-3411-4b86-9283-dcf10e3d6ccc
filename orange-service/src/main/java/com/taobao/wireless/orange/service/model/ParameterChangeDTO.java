package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterValueType;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;

@Data
public class ParameterChangeDTO {
    /**
     * 参数id
     */
    private String parameterId;

    /**
     * 参数键名
     */
    private String parameterKey;

    /**
     * 参数值类型
     */
    @EnumValidation(clazz = ParameterValueType.class, message = "参数值类型不合法")
    private ParameterValueType valueType;

    /**
     * 条件顺序
     */
    private List<String> conditionNamesOrder;

    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;

    /**
     * 变更类型
     */
    @EnumValidation(clazz = ChangeType.class, message = "变更类型不合法")
    private ChangeType changeType;

    /**
     * 描述
     */
    private String description;

    /**
     * 本次发布单涉及的参数条件变更
     */
    @Valid
    private List<ParameterConditionChangeDTO> parameterConditionChanges;
}
