package com.taobao.wireless.orange.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OTaskHandlerDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskHandlerDO;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.manager.model.TaskBO;
import com.taobao.wireless.orange.manager.model.TaskHandlerBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.TaskDetailDTO;
import com.taobao.wireless.orange.service.model.TaskHandlerQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 任务服务
 */
@Service
public class TaskService {

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private OTaskHandlerDAO taskHandlerDAO;

    /**
     * 查询我的任务列表
     *
     * @param query      查询条件
     * @param pagination 分页参数
     * @return 任务分页结果
     */
    public PaginationResult<TaskDetailDTO> queryMyTasks(TaskHandlerQueryDTO query, Pagination pagination) {
        // 转换查询条件
        TaskHandlerBO taskQuery = BeanUtil.createFromProperties(query, TaskHandlerBO.class);

        // 查询我的任务数据
        Page<TaskBO> pageResult = taskManager.queryMyTasks(taskQuery, pagination);

        // 转换结果
        return PageUtil.convert(pageResult, TaskDetailDTO.class);
    }

    /**
     * 忽略任务
     *
     * @param taskId
     */
    public void ignore(String taskId) {
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, ThreadContextUtil.getWorkerId())
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getStatus, TaskHandlerStatus.NO_NEED)
                .update();
    }

    public Map<TaskHandlerStatus, Long> countMyTaskByStatus() {
        return taskHandlerDAO.getCountGroupByStatus(ThreadContextUtil.getWorkerId());
    }

    /**
     * 转交任务
     *
     * @param taskId
     */
    public void transfer(String taskId, String targetUserId) {
        boolean exists = taskHandlerDAO.lambdaQuery()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, targetUserId)
                .exists();
        if (exists) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "目标用户已经存在该任务");
        }

        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, ThreadContextUtil.getWorkerId())
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getUserId, targetUserId)
                .update();
    }
}