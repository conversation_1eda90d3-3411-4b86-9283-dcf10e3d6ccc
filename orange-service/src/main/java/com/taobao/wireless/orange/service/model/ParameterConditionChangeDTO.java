package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ParameterConditionChangeDTO {
    /**
     * 参数id
     */
    private String parameterId;

    /**
     * 参数键名
     */
    private String conditionId;

    /**
     * 条件名
     */
    private String conditionName;

    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;

    /**
     * 参数条件值
     */
    @NotBlank(message = "参数条件值不能为空")
    private String value;

    /**
     * 变更类型
     */
    @EnumValidation(clazz = ChangeType.class, message = "变更类型不合法")
    private ChangeType changeType;
}
