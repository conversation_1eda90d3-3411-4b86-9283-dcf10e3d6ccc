package com.taobao.wireless.orange.service;


import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.manager.ConditionManager;
import com.taobao.wireless.orange.manager.model.ConditionBO;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConditionService {

    @Autowired
    private ConditionManager conditionManager;

    @AttributeValidate
    public PaginationResult<ConditionDetailDTO> query(@NotNull(message = "条件查询条件不能为空") ConditionQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(query, ConditionBO.class))
                .map(q -> conditionManager.query(q, pagination))
                .map(r -> {
                    PaginationResult<ConditionDetailDTO> result = BeanUtil.createFromProperties(r, PaginationResult.class);
                    result.setData(convertToDetailDTO(r.getRecords()));
                    return result;
                })
                .get();
    }

    public List<ConditionDetailDTO> convertToDetailDTO(List<ConditionBO> conditions) {
        return conditions.stream().map(c -> {
            ConditionDetailDTO detail = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
            var conditionVersion = c.getConditionVersion();
            detail.setGmtModified(conditionVersion.getGmtCreate());
            detail.setExpression(JSON.parse(conditionVersion.getExpression(), ConditionExpressionDTO.class));
            detail.setParameterConditions(BeanUtil.createFromProperties(c.getRelatedParameters(), ParameterConditionDTO.class));
            return detail;
        }).collect(Collectors.toList());
    }

    public Result<List<ConditionDTO>> getAll(ConditionQueryDTO query) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(q, ConditionBO.class))
                .map(q -> conditionManager.getAllOnlineConditions(q))
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    private List<ConditionDTO> convert(List<ConditionBO> conditionVersions) {
        return conditionVersions.stream().map(v -> {
            var conditionVersion = v.getConditionVersion();
            ConditionDTO c = BeanUtil.createFromProperties(conditionVersion, ConditionDTO.class);
            c.setExpression(JSON.parse(conditionVersion.getExpression(), ConditionExpressionDTO.class));

            c.setName(v.getName());
            c.setColor(v.getColor());
            return c;
        }).collect(Collectors.toList());
    }

    public Result<ConditionDetailDTO> getByConditionId(String conditionId) {
        return Pipe.of(conditionId)
                .map(id -> conditionManager.getOnlineConditionDetailByConditionId(id))
                .map(c -> {
                    ConditionDetailDTO condition = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
                    condition.setExpression(JSON.parse(c.getConditionVersion().getExpression(), ConditionExpressionDTO.class));
                    return condition;
                })
                .map(Result::new)
                .get();
    }

    Long create(ConditionBO condition) {
        return null;
    }

    Boolean update(ConditionBO condition) {
        return null;
    }

    Long countDevice(Long conditionId) {
        return null;
    }
}
