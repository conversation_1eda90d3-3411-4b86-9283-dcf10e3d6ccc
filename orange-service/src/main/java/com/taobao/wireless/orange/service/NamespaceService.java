package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.external.mtl.MtlService;
import com.taobao.wireless.orange.manager.NamespaceManager;
import com.taobao.wireless.orange.manager.model.ONamespaceBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.NamespaceCreateDTO;
import com.taobao.wireless.orange.service.model.NamespaceDTO;
import com.taobao.wireless.orange.service.model.NamespaceQueryDTO;
import com.taobao.wireless.orange.service.model.NamespaceUpdateDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


/**
 * 命名空间操作接口。
 */
@Service
public class NamespaceService {

    @Autowired
    private NamespaceManager namespaceManager;
    @Autowired
    private MtlService mtlService;

    /**
     * 获取所有命名空间的列表。
     *
     * @return Namespace对象列表
     */
    @AttributeValidate
    public PaginationResult<NamespaceDTO> query(@NotNull(message = "命名空间查询条件不能为空") NamespaceQueryDTO namespaceQueryDTO, Pagination pagination) {
        return Pipe.of(namespaceQueryDTO)
                .map(q -> BeanUtil.createFromProperties(q, ONamespaceBO.class))
                .map(q -> namespaceManager.query(q, pagination))
                .map(r -> PageUtil.convert(r, NamespaceDTO.class))
                .apply(r -> setBizName(r.getData()))
                .get();
    }
    /**
     * 创建新的命名空间。
     *
     * @param updateNamespace 要创建的Namespace对象
     */
    @AttributeValidate
    public Result<String> create(@NotNull(message = "命名空间对象不能为空") NamespaceCreateDTO updateNamespace) {
        return Pipe.of(updateNamespace)
                .map(n -> BeanUtil.createFromProperties(n, ONamespaceBO.class))
                .map(namespaceManager::create)
                .map(Result::new)
                .get();
    }

    /**
     * 获取指定命名空间的详细信息。
     *
     * @param namespaceId 命名空间ID
     * @return 包含详细信息的Namespace对象
     */
    @AttributeValidate
    public Result<NamespaceDTO> getByNamespaceId(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        return Pipe.of(namespaceManager.getByNamespaceId(namespaceId))
                .map(ns -> BeanUtil.createFromProperties(ns, NamespaceDTO.class))
                .map(Result::new)
                .get();
    }

    /**
     * 更新现有的命名空间信息。
     *
     * @param namespace 更新后的Namespace对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "命名空间对象不能为空") NamespaceUpdateDTO namespace) {
        Pipe.of(namespace)
                .map(n -> BeanUtil.createFromProperties(n, ONamespaceBO.class))
                .map(namespaceManager::update);
        return Result.success();
    }

    /**
     * 删除命名空间
     *
     * @param namespaceId 命名空间ID
     */
    @AttributeValidate
    public Result<Void> delete(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        namespaceManager.delete(namespaceId);
        // todo: 需要生成一个删除 namespace 的发布单
        return Result.success();
    }

    private void setBizName(List<NamespaceDTO> namespaces) {
        var moduleIds = namespaces.stream()
                .filter(n -> NamespaceBizType.MODULE.equals(n.getBizType()))
                .map(i -> Long.parseLong(i.getBizId()))
                .distinct()
                .toList();

        var moduleId2Module = mtlService.getModulesByModuleIds(moduleIds);

        namespaces.stream()
                .filter(n -> NamespaceBizType.MODULE.equals(n.getBizType()))
                .forEach(n -> {
                    Optional.ofNullable(moduleId2Module.get(Long.parseLong(n.getBizId())))
                            .ifPresent(m -> n.setBizName(m.getName()));
                });
    }
}
