package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum TaskBizType {
    // 发布单
    RELEASE_ORDER("RELEASE_ORDER"),
    // 命名空间
    NAMESPACE("NAMESPACE"),
    // 参数
    PARAMETER("PARAMETER"),
    // 条件
    CONDITION("CONDITION"),
    // 实验
    EXPERIMENT("EXPERIMENT");

    private final String code;
}
