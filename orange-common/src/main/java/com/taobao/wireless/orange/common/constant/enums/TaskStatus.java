package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {
    // 待处理
    PENDING("PENDING"),
    // 已完成
    COMPLETED("COMPLETED"),
    // 已取消
    CANCELED("CANCELED"),
    ;

    private final String code;

    /**
     * 判断任务是否已结束
     */
    public boolean isFinished() {
        return this == COMPLETED || this == CANCELED;
    }
}
