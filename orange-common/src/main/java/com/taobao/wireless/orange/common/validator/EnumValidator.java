package com.taobao.wireless.orange.common.validator;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.SneakyThrows;

import java.lang.reflect.Method;
import java.util.List;

/**
 * 枚举校验器
 *
 * <AUTHOR>
 */
public class EnumValidator implements ConstraintValidator<EnumValidation, Object> {
    private EnumValidation annotation;

    @Override
    public void initialize(EnumValidation constraintAnnotation) {
        this.annotation = constraintAnnotation;
    }

    @SneakyThrows
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        Object[] objects = annotation.clazz().getEnumConstants();
        Method method = annotation.clazz().getMethod(annotation.method());
        if (value instanceof List) {
            // 如果字段是列表，则需要确保列表中的每个元素都通过枚举值校验
            return ((List) value).stream().filter(i -> !this.isValid(objects, i, method)).count() == 0;
        } else {
            return this.isValid(objects, value, method);
        }
    }

    @SneakyThrows
    private boolean isValid(Object[] objects, Object i, Method method) {
        for (Object o : objects) {
            if (i.equals(o) || i.equals(method.invoke(o))) {
                return true;
            }
        }
        return false;
    }
}

