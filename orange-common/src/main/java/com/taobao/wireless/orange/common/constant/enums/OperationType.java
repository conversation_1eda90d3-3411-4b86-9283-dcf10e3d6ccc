package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OperationType {
    // 创建发布单
    CREATE("CREATE"),
    // 推送 beta
    PUSH_BETA("PUSH_BETA"),
    // 扫码 beta
    SCAN_BETA("SCAN_BETA"),
    // 申请发布
    APPLY_RELEASE("APPLY_RELEASE"),
    // 定量灰度
    GRAY("GRAY"),
    // 百分比灰度
    RATIO_GRAY("RATIO_GRAY"),
    // 发起验证
    START_VERIFY("START_VERIFY"),
    // 验证
    VERIFY_REPLY("VERIFY_REPLY"),
    // 正式发布
    RELEASE("RELEASE"),
    // 取消发布
    CANCEL("CANCEL");

    private final String code;
}
